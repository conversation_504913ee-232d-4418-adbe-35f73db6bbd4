
#include "sys_check.h"
#include <stdio.h>
#include "bsp_flash.h"
#include "bsp_sd.h"
#include "bsp_rtc.h"
#include "dev_oled.h"

void System_SelfCheck(void)
{
    printf("====System Self Check====\r\n");

    // Flash 检查
    uint32_t flash_id = 0;
    if (flash_read_id(&flash_id) == 0)
        printf("FLASH: OK\r\n");
    else
        printf("FLASH: ERROR\r\n");

    // TF 卡检测
    if (tf_card_present())
        printf("TF Card: OK\r\n");
    else
        printf("TF Card: ERROR\r\n");

    // OLED 检查：显示测试文字，若显示正常则说明初始化成功
    OLED_ShowString(0, 1, "OLED Check");
    printf("OLED: OK\r\n");

    // RTC 检查
    if (rtc_check() == 0)
        printf("RTC: OK\r\n");
    else
        printf("RTC: ERROR\r\n");

    printf("====Check End====\r\n");
}
