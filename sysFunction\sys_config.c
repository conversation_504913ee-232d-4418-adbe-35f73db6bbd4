
#include "sys_config.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "bsp_sd.h"
#include "bsp_flash.h"

#define CONFIG_FLASH_ADDR  (0x0807F800)  // 替换为你的实际地址

static float g_ratio = 1.0;
static float g_limit = 100.0;

typedef struct {
    float ratio;
    float limit;
} ParamConfig;

// 从 config.ini 读取参数
void Config_ReadFile(void)
{
    FIL file;
    char line[64];

    if (f_open(&file, "0:/config.ini", FA_READ) != FR_OK) {
        printf("No such file.\r\n");
        return;
    }

    while (f_gets(line, sizeof(line), &file)) {
        if (sscanf(line, "Ch0_ratio=%f", &g_ratio) == 1) continue;
        if (sscanf(line, "Ch0_limit=%f", &g_limit) == 1) continue;
    }

    f_close(&file);

    printf("Config File Loaded.\r\n");
    printf("Ratio = %.2f\r\n", g_ratio);
    printf("Limit = %.2f\r\n", g_limit);
}

// 设置 ratio
void Config_SetRatio(void)
{
    printf("Current Ratio: %.2f\r\n", g_ratio);
    printf("New Ratio: ");
    float val;
    if (scanf("%f", &val) && val >= 0 && val <= 100) {
        g_ratio = val;
        printf("Ratio Updated: %.2f\r\n", g_ratio);
    } else {
        printf("Invalid Ratio.\r\n");
    }
}

// 设置 limit
void Config_SetLimit(void)
{
    printf("Current Limit: %.2f\r\n", g_limit);
    printf("New Limit: ");
    float val;
    if (scanf("%f", &val) && val >= 0 && val <= 500) {
        g_limit = val;
        printf("Limit Updated: %.2f\r\n", g_limit);
    } else {
        printf("Invalid Limit.\r\n");
    }
}

// 写入 Flash
void Config_SaveToFlash(void)
{
    ParamConfig cfg = { g_ratio, g_limit };
    flash_erase(CONFIG_FLASH_ADDR);
    flash_write(CONFIG_FLASH_ADDR, (uint8_t*)&cfg, sizeof(cfg));
    printf("Config Saved.\r\n");
}

// 读取 Flash
void Config_ReadFromFlash(void)
{
    ParamConfig cfg;
    memcpy(&cfg, (void*)CONFIG_FLASH_ADDR, sizeof(cfg));
    g_ratio = cfg.ratio;
    g_limit = cfg.limit;

    printf("Config Read From Flash:\r\n");
    printf("Ratio = %.2f\r\n", g_ratio);
    printf("Limit = %.2f\r\n", g_limit);
}

// 提供外部调用接口
float Get_Ratio(void) { return g_ratio; }
float Get_Limit(void) { return g_limit; }
