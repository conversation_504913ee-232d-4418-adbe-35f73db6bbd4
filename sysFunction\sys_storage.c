
#include "sys_storage.h"
#include "ff.h"
#include <stdio.h>
#include <string.h>

#define MAX_LINES_PER_FILE 10

static uint8_t log_id = 0;
static uint8_t sample_line_count = 0;
static uint8_t overlimit_line_count = 0;
static uint8_t hide_line_count = 0;

static FIL sample_file, over_file, hide_file, log_file;
static char sample_filename[64], over_filename[64], hide_filename[64], log_filename[64];

static void make_timestamp(char* out, rtc_time_t* t)
{
    sprintf(out, "%04d%02d%02d%02d%02d%02d",
            t->year, t->month, t->day, t->hour, t->minute, t->second);
}

static void open_new_file(FIL* f, const char* folder, const char* prefix, char* filename, rtc_time_t* t)
{
    char ts[32];
    make_timestamp(ts, t);
    sprintf(filename, "0:/%s/%s%s.txt", folder, prefix, ts);
    f_open(f, filename, FA_WRITE | FA_CREATE_ALWAYS);
}

void Storage_Init(void)
{
    f_mkdir("0:/sample");
    f_mkdir("0:/overLimit");
    f_mkdir("0:/log");
    f_mkdir("0:/hideData");

    // 从 Flash 中读取上电次数，这里简化为 static 变量 log_id
    sprintf(log_filename, "0:/log/log%d.txt", log_id++);
    f_open(&log_file, log_filename, FA_WRITE | FA_CREATE_ALWAYS);
}

void Storage_SaveLog(const char* log)
{
    UINT bw;
    f_write(&log_file, log, strlen(log), &bw);
    f_write(&log_file, "\r\n", 2, &bw);
}

void Storage_SaveSample(rtc_time_t* time, float voltage, bool overlimit, bool hidden_mode)
{
    char line[128];
    UINT bw;

    // 选择保存内容
    if (hidden_mode)
    {
        if (hide_line_count == 0)
            open_new_file(&hide_file, "hideData", "hideData", hide_filename, time);

        uint32_t unix = rtc_to_unix(time);
        uint16_t intp = (uint16_t)voltage;
        uint16_t frac = (uint16_t)((voltage - intp) * 65536);

        sprintf(line, "%08X%04X%04X%s\r\n", unix, intp, frac, overlimit ? "*" : "");
        f_write(&hide_file, line, strlen(line), &bw);

        hide_line_count++;
        if (hide_line_count >= MAX_LINES_PER_FILE) {
            f_close(&hide_file);
            hide_line_count = 0;
        }
    }
    else
    {
        // sample 文件
        if (sample_line_count == 0)
            open_new_file(&sample_file, "sample", "sampleData", sample_filename, time);

        sprintf(line, "%02d:%02d:%02d %.2fV\r\n", time->hour, time->minute, time->second, voltage);
        f_write(&sample_file, line, strlen(line), &bw);

        sample_line_count++;
        if (sample_line_count >= MAX_LINES_PER_FILE) {
            f_close(&sample_file);
            sample_line_count = 0;
        }
    }

    // overLimit 数据总是写入
    if (overlimit)
    {
        if (over_line_count == 0)
            open_new_file(&over_file, "overLimit", "overLimit", over_filename, time);

        sprintf(line, "%02d:%02d:%02d %.2fV\r\n", time->hour, time->minute, time->second, voltage);
        f_write(&over_file, line, strlen(line), &bw);

        over_line_count++;
        if (over_line_count >= MAX_LINES_PER_FILE) {
            f_close(&over_file);
            over_line_count = 0;
        }
    }
}
