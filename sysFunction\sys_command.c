
#include "sys_command.h"
#include <string.h>
#include <stdio.h>

#include "sys_check.h"
#include "sys_sample.h"
#include "sys_config.h"
#include "sys_rtc.h"
#include "sys_storage.h"

void CMD_Process(const char* cmdline)
{
    if (strcmp(cmdline, "test") == 0) {
        System_SelfCheck();
        Storage_SaveLog("SelfCheck done");
    }
    else if (strcmp(cmdline, "start") == 0) {
        Sample_Start();
        Storage_SaveLog("Start sampling");
    }
    else if (strcmp(cmdline, "stop") == 0) {
        Sample_Stop();
        Storage_SaveLog("Stop sampling");
    }
    else if (strcmp(cmdline, "conf") == 0) {
        Config_ReadFile();
        Storage_SaveLog("Load config.ini");
    }
    else if (strcmp(cmdline, "ratio") == 0) {
        Config_SetRatio();
    }
    else if (strcmp(cmdline, "limit") == 0) {
        Config_SetLimit();
    }
    else if (strcmp(cmdline, "config save") == 0) {
        Config_SaveToFlash();
        Storage_SaveLog("Save config to flash");
    }
    else if (strcmp(cmdline, "config read") == 0) {
        Config_ReadFromFlash();
    }
    else if (strcmp(cmdline, "RTC now") == 0) {
        SYS_RTC_ShowNow();
    }
    else if (strcmp(cmdline, "RTC Config") == 0) {
        char buf[64];
        printf("Input Datetime:\r\n");
        scanf("%[^
]", buf);
        SYS_RTC_Config(buf);
        Storage_SaveLog("RTC Config");
    }
    else if (strcmp(cmdline, "hide") == 0) {
        Sample_SetHiddenMode(true);
        Storage_SaveLog("Hidden mode ON");
    }
    else if (strcmp(cmdline, "unhide") == 0) {
        Sample_SetHiddenMode(false);
        Storage_SaveLog("Hidden mode OFF");
    }
    else {
        printf("Unknown command: %s\r\n", cmdline);
    }
}
