
#include "sys_sample.h"
#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include "bsp_adc.h"
#include "bsp_led.h"
#include "dev_oled.h"
#include "sys_config.h"
#include "sys_rtc.h"
#include "bsp_rtc.h"

typedef enum { IDLE = 0, SAMPLING } SampleState;
static SampleState g_state = IDLE;

static uint8_t g_sample_period = 5;
static uint32_t g_last_sample_time = 0;

static bool g_hidden = false;  // hide 模式

void Sample_SetHiddenMode(bool enable) {
    g_hidden = enable;
    printf("Hidden Mode: %s\r\n", g_hidden ? "ON" : "OFF");
}

bool Sample_IsHidden(void) {
    return g_hidden;
}

void Sample_Start(void)
{
    g_state = SAMPLING;
    g_last_sample_time = rtc_millis();
    LED1_On();
    printf("Sampling Started.\r\n");
}

void Sample_Stop(void)
{
    g_state = IDLE;
    LED1_Off();
    LED2_Off();
    OLED_Clear();
    OLED_ShowString(0, 0, "system idle");
    printf("Sampling Stopped.\r\n");
}

void Sample_Toggle(void)
{
    if (g_state == IDLE)
        Sample_Start();
    else
        Sample_Stop();
}

void Sample_PeriodSet(uint8_t sec)
{
    if (sec == 5 || sec == 10 || sec == 15) {
        g_sample_period = sec;
        printf("Sample Period Set: %ds\r\n", g_sample_period);
    }
}

void Sample_PeriodDefault(void) { Sample_PeriodSet(5); }
void Sample_PeriodUp(void)      { Sample_PeriodSet(15); }
void Sample_PeriodDown(void)    { Sample_PeriodSet(10); }

// 编码工具函数
static void print_hex32(uint32_t val)
{
    for (int i = 28; i >= 0; i -= 4)
        putchar("0123456789ABCDEF"[(val >> i) & 0xF]);
}

void Sample_Update(void)
{
    if (g_state != SAMPLING) return;

    uint32_t now = rtc_millis();
    if (now - g_last_sample_time >= g_sample_period * 1000) {
        g_last_sample_time = now;

        float raw = adc_read_voltage();
        float ratio = Get_Ratio();
        float v_eng = raw * ratio;

        rtc_time_t now_time;
        rtc_get_time(&now_time);

        float limit = Get_Limit();
        bool over = v_eng > limit;

        if (over) LED2_On(); else LED2_Off();

        if (g_hidden) {
            // 1. 时间戳 → Unix
            uint32_t unix = rtc_to_unix(&now_time);
            print_hex32(unix);

            // 2. 电压编码
            uint16_t int_part = (uint16_t)v_eng;
            uint16_t frac_part = (uint16_t)((v_eng - int_part) * 65536);

            print_hex32((int_part << 16) | frac_part);

            if (over) putchar('*');
            putchar('\r');
            putchar('\n');
        } else {
            printf("%02d:%02d:%02d %.2fV", now_time.hour, now_time.minute, now_time.second, v_eng);
            if (over)
                printf(" OverLimit (limit: %.2f)", limit);
            printf("\r\n");
        }

        // OLED 不受 hide 模式影响
        char time_buf[16], volt_buf[16];
        sprintf(time_buf, "%02d:%02d:%02d", now_time.hour, now_time.minute, now_time.second);
        sprintf(volt_buf, "%.2f V", v_eng);
        OLED_Clear();
        OLED_ShowString(0, 0, time_buf);
        OLED_ShowString(0, 1, volt_buf);

        LED1_Toggle();
    }
}
