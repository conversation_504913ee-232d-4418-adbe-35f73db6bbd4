
#include "sys_rtc.h"
#include "bsp_rtc.h"  // 假设你已有底层 rtc 驱动
#include <stdio.h>
#include <string.h>

// 时间解析辅助
static int parse_datetime(const char* input, rtc_time_t* time)
{
    return sscanf(input, "%d%*[- /.]%d%*[- /.]%d%*[^0-9]%d%*[:]%d%*[:]%d",
                  &time->year, &time->month, &time->day,
                  &time->hour, &time->minute, &time->second) == 6;
}

void SYS_RTC_Config(const char* input)
{
    rtc_time_t rtc_time;

    if (!parse_datetime(input, &rtc_time)) {
        printf("Invalid datetime format.\r\n");
        return;
    }

    rtc_set_time(&rtc_time);  // 调用你已有的底层函数
    printf("RTC Time Set: %04d-%02d-%02d %02d:%02d:%02d\r\n",
           rtc_time.year, rtc_time.month, rtc_time.day,
           rtc_time.hour, rtc_time.minute, rtc_time.second);
}

void SYS_RTC_ShowNow(void)
{
    rtc_time_t now;
    rtc_get_time(&now);  // 读取当前 RTC 时间

    printf("RTC Now: %04d-%02d-%02d %02d:%02d:%02d\r\n",
           now.year, now.month, now.day,
           now.hour, now.minute, now.second);
}
